{"Providers": [{"name": "openrouter", "api_base_url": "https://openrouter.ai/api/v1/chat/completions", "api_key": "sk-xxx", "models": ["google/gemini-2.5-pro-preview", "anthropic/claude-sonnet-4", "anthropic/claude-3.5-sonnet", "anthropic/claude-3.7-sonnet:thinking"], "transformer": {"use": ["openrouter"]}}, {"name": "deepseek", "api_base_url": "https://api.deepseek.com/chat/completions", "api_key": "sk-xxx", "models": ["deepseek-chat", "deepseek-reasoner"], "transformer": {"use": ["deepseek"], "deepseek-chat": {"use": ["tooluse"]}}}, {"name": "ollama", "api_base_url": "http://localhost:11434/v1/chat/completions", "api_key": "ollama", "models": ["qwen2.5-coder:latest"]}, {"name": "gemini", "api_base_url": "https://generativelanguage.googleapis.com/v1beta/models/", "api_key": "sk-xxx", "models": ["gemini-2.5-flash", "gemini-2.5-pro"], "transformer": {"use": ["gemini"]}}, {"name": "volcengine", "api_base_url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions", "api_key": "sk-xxx", "models": ["deepseek-v3-250324", "deepseek-r1-250528"], "transformer": {"use": ["deepseek"]}}, {"name": "siliconflow", "api_base_url": "https://api.siliconflow.cn/v1/chat/completions", "api_key": "sk-xxx", "models": ["moonshotai/Kimi-K2-Instruct"], "transformer": {"use": [["maxtoken", {"max_tokens": 16384}]]}}, {"name": "modelscope", "api_base_url": "https://api-inference.modelscope.cn/v1/chat/completions", "api_key": "", "models": ["Qwen/Qwen3-Coder-480B-A35B-Instruct", "Qwen/Qwen3-235B-A22B-Thinking-2507"], "transformer": {"use": [["maxtoken", {"max_tokens": 65536}], "enhancetool"], "Qwen/Qwen3-235B-A22B-Thinking-2507": {"use": ["reasoning"]}}}, {"name": "dashscope", "api_base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", "api_key": "", "models": ["qwen3-coder-plus"], "transformer": {"use": [["maxtoken", {"max_tokens": 65536}], "enhancetool"]}}], "Router": {"default": "deepseek,deepseek-chat", "background": "o<PERSON>ma,qwen2.5-coder:latest", "think": "deepseek,deepseek-reasoner", "longContext": "openrouter,google/gemini-2.5-pro-preview", "longContextThreshold": 60000, "webSearch": "gemini,gemini-2.5-flash"}, "APIKEY": "your-secret-key", "HOST": "0.0.0.0", "API_TIMEOUT_MS": 600000}