{"app": {"title": "Claude <PERSON> Router", "save": "保存", "save_and_restart": "保存并重启", "cancel": "取消", "edit": "编辑", "remove": "移除", "delete": "删除", "settings": "设置", "selectFile": "选择文件", "config_saved_success": "配置保存成功", "config_saved_failed": "配置保存失败", "config_saved_restart_success": "配置保存并服务重启成功", "config_saved_restart_failed": "配置保存并服务重启失败"}, "login": {"title": "登录到您的账户", "description": "请输入您的API密钥以访问配置面板", "apiKey": "API密钥", "apiKeyPlaceholder": "请输入您的API密钥", "signIn": "登录", "invalidApiKey": "API密钥无效", "configError": "配置未加载", "validating": "正在验证API密钥..."}, "toplevel": {"title": "通用设置", "log": "启用日志", "claude_path": "<PERSON> 路径", "host": "主机", "port": "端口", "apikey": "API 密钥", "timeout": "API 超时时间 (毫秒)"}, "transformers": {"title": "自定义转换器", "path": "路径", "project": "项目", "remove": "移除", "add": "添加自定义转换器", "edit": "编辑自定义转换器", "delete": "删除自定义转换器", "delete_transformer_confirm": "您确定要删除此自定义转换器吗？", "parameters": "参数"}, "providers": {"title": "供应商", "name": "名称", "api_base_url": "API 基础地址", "api_key": "API 密钥", "models": "模型", "models_placeholder": "输入模型名称并按回车键添加", "add_model": "添加模型", "select_models": "选择模型", "remove": "移除", "add": "添加供应商", "edit": "编辑供应商", "delete": "删除", "cancel": "取消", "delete_provider_confirm": "您确定要删除此供应商吗？", "test_connectivity": "测试连通性", "testing": "测试中...", "connection_successful": "连接成功！", "connection_failed": "连接失败！", "missing_credentials": "缺少 API 基础地址或 API 密钥", "fetch_available_models": "获取可用模型", "fetching_models": "获取模型中...", "fetch_models_failed": "获取模型失败", "transformers": "转换器", "select_transformer": "选择转换器", "no_transformers": "无可用转换器", "provider_transformer": "供应商转换器", "model_transformers": "模型转换器", "transformer_parameters": "转换器参数", "add_parameter": "添加参数", "parameter_name": "参数名称", "parameter_value": "参数值", "selected_transformers": "已选转换器"}, "router": {"title": "路由", "default": "默认", "background": "后台", "think": "思考", "longContext": "长上下文", "longContextThreshold": "上下文阈值", "webSearch": "网络搜索", "selectModel": "选择一个模型...", "searchModel": "搜索模型...", "noModelFound": "未找到模型."}, "json_editor": {"title": "JSON 编辑器", "save": "保存", "saving": "保存中...", "cancel": "取消", "save_failed": "配置保存失败", "save_and_restart": "保存并重启"}}